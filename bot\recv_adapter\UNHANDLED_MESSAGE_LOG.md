# 未处理消息日志功能

## 功能概述

本功能在消息处理流程中增加了原始消息数据的保存和未处理消息的日志记录功能。

## 主要改动

### 1. StandardMessage模型增强

在 `framework/core/models.py` 中的 `StandardMessage` 类增加了 `raw_data` 字段：

```python
class StandardMessage(BaseModel):
    # ... 其他字段 ...
    
    # 原始消息数据
    raw_data: Optional[Dict[str, Any]] = None
```

### 2. 消息处理器更新

在 `framework/core/message_processor.py` 中，所有版本的消息标准化方法都会保存原始数据：

- `normalize_message_v1()` - 保存V1版本原始数据
- `normalize_message_v1_5()` - 保存V1.5版本原始数据  
- `normalize_message_v2()` - 保存V2版本原始数据
- `normalize_message_v3()` - 保存V3版本原始数据

### 3. 未处理消息日志记录

在 `main.py` 中增加了 `_log_unhandled_message()` 方法，当消息没有被处理器处理时，会将消息信息（包括原始数据）写入日志文件。

### 4. 配置选项

在 `config.py` 中增加了相关配置：

```python
# 未处理消息日志配置
unhandled_message_log_enabled: bool = True  # 是否启用未处理消息日志
unhandled_message_log_file: str = "logs/unhandled_messages.log"  # 日志文件路径
```

## 日志格式

未处理消息日志采用JSON格式，每行一条记录：

```json
{
  "timestamp": "2025-08-07T10:30:45.123456",
  "msg_id": 12345,
  "msg_type": 1,
  "msg_name": "AddMsgs",
  "version": 1,
  "wxid": "test_wxid",
  "from_user": "test_user",
  "to_user": "test_target",
  "content": "消息内容（截取前200字符）",
  "is_group": false,
  "group_id": null,
  "raw_data": {
    "msg_id": 12345,
    "msg_type": 1,
    "content": "完整的原始消息内容",
    "from_user_name": "test_user",
    "create_time": **********,
    "account_wxid": "test_wxid"
  }
}
```

## 配置方法

在 `.env` 文件中添加以下配置：

```bash
# 未处理消息日志配置
UNHANDLED_MESSAGE_LOG_ENABLED=true
UNHANDLED_MESSAGE_LOG_FILE=logs/unhandled_messages.log
```

## 使用场景

1. **调试消息处理逻辑** - 通过查看未处理消息的原始数据，了解哪些消息没有被正确处理
2. **消息格式分析** - 分析不同版本和来源的消息格式差异
3. **问题排查** - 当消息处理出现问题时，可以查看完整的原始数据进行分析
4. **功能完善** - 基于未处理消息的类型和内容，完善消息处理逻辑

## 注意事项

1. 日志文件会持续增长，建议定期清理或设置日志轮转
2. 原始数据可能包含敏感信息，请注意日志文件的安全性
3. 可以通过设置 `UNHANDLED_MESSAGE_LOG_ENABLED=false` 来禁用此功能
4. 日志文件路径相对于程序运行目录，确保有写入权限

## 文件位置

- 配置文件：`config.py`
- 模型定义：`framework/core/models.py`
- 消息处理器：`framework/core/message_processor.py`
- 主程序：`main.py`
- 环境配置示例：`.env.example`
