"""
消息处理器 - 处理不同类型的消息
"""
import xml.etree.ElementTree as ET
import random
import time
import aiohttp
from typing import Dict, Any, Optional
from loguru import logger

from framework.core.models import StandardMessage, MessageType, MessageType_49


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self):
        self.handlers = {
            MessageType.文本: self.handle_text_message,
            MessageType.图片: self.handle_image_message,
            MessageType.语音: self.handle_voice_message,
            MessageType.视频: self.handle_video_message,
            MessageType.表情: self.handle_emoji_message,
            MessageType.应用: self.handle_app_message,
            MessageType.文件: self.handle_file_message,
            MessageType.名片: self.handle_card_message,
            MessageType.位置: self.handle_location_message,
            MessageType.查看: self.handle_view_message,
        }
    
    async def handle_message(self, message: StandardMessage) -> bool:
        """
        处理消息的主入口

        Args:
            message: 标准化后的消息

        Returns:
            bool: 是否成功处理
        """
        try:
            # 检查是否是特殊消息类型
            if hasattr(message, 'msg_name') and message.msg_name in ["ModContacts", "DelContacts", "Offline"]:
                return await self._handle_special_message(message, message.msg_name)

            # 检查是否是支持的消息类型
            if message.msg_type not in self.handlers:
                logger.debug(f"Unsupported message type: {message.msg_type}")
                return False

            # 调用对应的处理器
            handler = self.handlers[message.msg_type]
            return await handler(message)

        except Exception as e:
            logger.error(f"Error handling message {message.msg_id}: {e}")
            return False

    async def _handle_special_message(self, message: StandardMessage, msg_name: str) -> bool:
        """处理特殊消息类型（如联系人变更、掉线等）"""
        logger.info(f"Handling special message: {msg_name}")

        if msg_name == "ModContacts":
            return await self._handle_contact_change(message)
        elif msg_name == "DelContacts":
            return await self._handle_contact_delete(message)
        elif msg_name == "Offline":
            return await self._handle_offline(message)
        else:
            logger.debug(f"Unknown special message type: {msg_name}")
            return False

    async def _handle_contact_change(self, message: StandardMessage) -> bool:
        """处理联系人变更"""
        logger.info(f"Contact changed: {message.content}")
        # TODO: 实现联系人变更处理逻辑
        return True

    async def _handle_contact_delete(self, message: StandardMessage) -> bool:
        """处理联系人删除"""
        logger.info(f"Contact deleted: {message.content}")
        # TODO: 实现联系人删除处理逻辑
        return True

    async def _handle_offline(self, message: StandardMessage) -> bool:
        """处理掉线通知"""
        logger.warning(f"Account offline: {message.content}")
        # TODO: 实现掉线处理逻辑
        return True

    async def handle_text_message(self, message: StandardMessage) -> bool:
        """处理文本消息"""
        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            logger.debug(f"Group text message in {message.group_id} from {message.from_user_name}: {message.content}")
            if message.from_user_name.endswith("@openim"):
                logger.info(f"OpenIM message: {message.content}")
                # 处理OpenIM消息并转发
                return await self._process_and_forward_text_message(message)
        else:
            logger.debug(f"Private text message from {message.from_user_name}: {message.content}")

        # 简单处理，返回，实际处理由下级程序完成
        return False

    
    async def _handle_message_by_type(self, message: StandardMessage, msg_type_name: str) -> bool:
        """
        通用的消息处理方法

        Args:
            message: 消息对象
            msg_type_name: 消息类型名称

        Returns:
            bool: 是否成功处理
        """
        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            logger.info(f"Processing group {msg_type_name} message in {message.group_id} from {message.from_user_name}")
        else:
            logger.info(f"Processing private {msg_type_name} message from {message.from_user_name}")

        # 简单处理，返回成功，实际处理由下级程序完成
        return True

    async def handle_image_message(self, message: StandardMessage) -> bool:
        """处理图片消息"""
        return await self._handle_message_by_type(message, "image")

    async def handle_voice_message(self, message: StandardMessage) -> bool:
        """处理语音消息"""
        return await self._handle_message_by_type(message, "voice")

    async def handle_video_message(self, message: StandardMessage) -> bool:
        """处理视频消息"""
        return await self._handle_message_by_type(message, "video")

    async def handle_emoji_message(self, message: StandardMessage) -> bool:
        """处理表情消息"""
        return await self._handle_message_by_type(message, "emoji")

    async def handle_app_message(self, message: StandardMessage) -> bool:
        """处理应用消息（群公告、链接、小程序等）"""
        try:
            # 解析XML内容判断具体的应用消息类型
            app_msg_type = self._parse_app_message_type(message.content)

            # 根据应用消息类型进行处理
            if app_msg_type == MessageType_49.群公告:
                return await self._handle_group_announcement(message)
            elif app_msg_type == MessageType_49.链接:
                return await self._handle_link_message(message)
            elif app_msg_type == MessageType_49.小程序:
                return await self._handle_mini_program(message)
            elif app_msg_type == MessageType_49.文件:
                return await self._handle_app_file_message(message)
            elif app_msg_type == MessageType_49.转账:
                return await self._handle_transfer_message(message)
            elif app_msg_type == MessageType_49.红包:
                return await self._handle_red_packet_message(message)
            else:
                logger.debug(f"Unknown app message type: {app_msg_type}, content: {message.content[:100]}...")
                return await self._handle_unknown_app_message(message)

        except Exception as e:
            logger.error(f"Error handling app message {message.msg_id}: {e}")
            return False

    async def handle_file_message(self, message: StandardMessage) -> bool:
        """处理文件消息"""
        return await self._handle_message_by_type(message, "file")

    async def handle_card_message(self, message: StandardMessage) -> bool:
        """处理名片消息"""
        return await self._handle_message_by_type(message, "card")

    async def handle_location_message(self, message: StandardMessage) -> bool:
        """处理位置消息"""
        return await self._handle_message_by_type(message, "location")

    async def handle_view_message(self, message: StandardMessage) -> bool:
        """处理查看消息"""
        return await self._handle_message_by_type(message, "view")

    def _parse_app_message_type(self, content: str) -> int:
        """
        解析应用消息类型

        Args:
            content: 消息内容（通常是XML格式）
            msg_source: 消息源信息

        Returns:
            int: MessageType_49中定义的消息类型
        """
        try:
            # 如果内容为空或不是XML
            if not content or not content.strip().startswith('<'):
                logger.debug(f"Non-XML app message content: {content[:50]}...")
                return -1

            # 解析XML内容
            try:
                root = ET.fromstring(content)

                # 检查根元素类型
                if root.tag == "msg":
                    # 检查appmsg类型
                    appmsg = root.find("appmsg")
                    if appmsg is not None:
                        app_type = appmsg.get("type")
                        if app_type:
                            return self._map_xml_type_to_message_type(int(app_type))

            except ET.ParseError as e:
                logger.debug(f"XML parse error: {e}, trying alternative parsing...")

        except Exception as e:
            logger.error(f"Error parsing app message type: {e}")

        return -1

    def _map_xml_type_to_message_type(self, xml_type: int) -> int:
        """
        将XML中的type值映射到MessageType_49

        Args:
            xml_type: XML中的type属性值

        Returns:
            int: MessageType_49中的对应值
        """
        # 根据微信的XML type映射
        type_mapping = {
            5: MessageType_49.链接,      # 链接分享
            6: MessageType_49.文件,      # 文件
            33: MessageType_49.小程序,    # 小程序
            87: MessageType_49.群公告,    # 群公告
            2000: MessageType_49.转账,   # 转账
            2001: MessageType_49.红包,   # 红包
        }

        return type_mapping.get(xml_type, MessageType_49.链接)

    async def _handle_group_announcement(self, message: StandardMessage) -> bool:
        """处理群公告消息"""
        logger.info(f"Processing group announcement: {message.content[:100]}...")

        # 解析群公告内容
        announcement_content = self._parse_group_announcement(message.content)

        if announcement_content:
            logger.info(f"Group announcement from {message.from_user_name}: {announcement_content[:50]}...")

            # 处理群公告并转发到HTTP接口
            return await self._process_and_forward_announcement(message, announcement_content)

        return True

    async def _handle_link_message(self, message: StandardMessage) -> bool:
        """处理链接分享消息"""
        logger.info(f"Processing link message: {message.content[:100]}...")

        # 解析链接信息
        link_info = self._parse_link_message(message.content)

        if link_info:
            logger.info(f"Link shared: {link_info.get('title', 'No title')} - {link_info.get('url', 'No URL')}")
            # TODO: 实现链接消息处理逻辑

        return True

    async def _handle_mini_program(self, message: StandardMessage) -> bool:
        """处理小程序消息"""
        logger.info(f"Processing mini program: {message.content[:100]}...")

        # 解析小程序信息
        mini_program_info = self._parse_mini_program(message.content)

        if mini_program_info:
            logger.info(f"Mini program: {mini_program_info.get('title', 'No title')}")
            # TODO: 实现小程序消息处理逻辑

        return True

    async def _handle_app_file_message(self, message: StandardMessage) -> bool:
        """处理应用文件消息"""
        logger.info(f"Processing app file message: {message.content[:100]}...")

        # 解析文件信息
        file_info = self._parse_app_file_message(message.content)

        if file_info:
            logger.info(f"App file: {file_info.get('title', 'No title')}")
            # TODO: 实现应用文件消息处理逻辑

        return True

    async def _handle_transfer_message(self, message: StandardMessage) -> bool:
        """处理转账消息"""
        logger.info(f"Processing transfer message: {message.content[:100]}...")

        # 解析转账信息
        transfer_info = self._parse_transfer_message(message.content)

        if transfer_info:
            logger.info(f"Transfer: {transfer_info.get('amount', 'Unknown amount')}")
            # TODO: 实现转账消息处理逻辑

        return True

    async def _handle_red_packet_message(self, message: StandardMessage) -> bool:
        """处理红包消息"""
        logger.info(f"Processing red packet message: {message.content[:100]}...")

        # 解析红包信息
        red_packet_info = self._parse_red_packet_message(message.content)

        if red_packet_info:
            logger.info(f"Red packet: {red_packet_info.get('title', 'No title')}")
            # TODO: 实现红包消息处理逻辑

        return True

    async def _handle_unknown_app_message(self, message: StandardMessage) -> bool:
        """处理未知类型的应用消息"""
        logger.debug(f"Processing unknown app message: {message.content[:100]}...")

        # 记录未知类型的消息以便后续分析
        logger.debug(f"Unknown app message details - msg_source: {message.msg_source}, content: {message.content}")

        # TODO: 实现未知应用消息处理逻辑
        return False

    def _parse_group_announcement(self, content: str) -> str:
        """解析群公告XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return ""

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                des_elem = appmsg.find("textannouncement")
                if des_elem is not None and des_elem.text:
                    return des_elem.text

            return ""

        except Exception as e:
            logger.error(f"Error parsing group announcement: {e}")
            return ""

    def _parse_link_message(self, content: str) -> Optional[Dict[str, Any]]:
        """解析链接分享XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return {"title": "链接分享", "url": content}

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                title_elem = appmsg.find("title")
                des_elem = appmsg.find("des")
                url_elem = appmsg.find("url")
                thumburl_elem = appmsg.find("thumburl")

                return {
                    "title": title_elem.text if title_elem is not None else "",
                    "description": des_elem.text if des_elem is not None else "",
                    "url": url_elem.text if url_elem is not None else "",
                    "thumb_url": thumburl_elem.text if thumburl_elem is not None else "",
                    "type": "link"
                }

            return {"title": "链接分享", "url": content}

        except Exception as e:
            logger.error(f"Error parsing link message: {e}")
            return {"title": "链接分享", "url": content}

    def _parse_mini_program(self, content: str) -> Optional[Dict[str, Any]]:
        """解析小程序XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return {"title": "小程序", "content": content}

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                title_elem = appmsg.find("title")
                des_elem = appmsg.find("des")

                # 查找weappinfo节点（小程序特有）
                weappinfo = appmsg.find("weappinfo")
                if weappinfo is not None:
                    username_elem = weappinfo.find("username")
                    appid_elem = weappinfo.find("appid")

                    return {
                        "title": title_elem.text if title_elem is not None else "",
                        "description": des_elem.text if des_elem is not None else "",
                        "username": username_elem.text if username_elem is not None else "",
                        "appid": appid_elem.text if appid_elem is not None else "",
                        "type": "mini_program"
                    }

                return {
                    "title": title_elem.text if title_elem is not None else "",
                    "description": des_elem.text if des_elem is not None else "",
                    "type": "mini_program"
                }

            return {"title": "小程序", "content": content}

        except Exception as e:
            logger.error(f"Error parsing mini program: {e}")
            return {"title": "小程序", "content": content}

    def _parse_app_file_message(self, content: str) -> Optional[Dict[str, Any]]:
        """解析应用文件XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return {"title": "文件", "content": content}

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                title_elem = appmsg.find("title")
                des_elem = appmsg.find("des")

                # 查找appattach节点（文件特有）
                appattach = appmsg.find("appattach")
                if appattach is not None:
                    totallen_elem = appattach.find("totallen")
                    attachid_elem = appattach.find("attachid")
                    fileext_elem = appattach.find("fileext")

                    return {
                        "title": title_elem.text if title_elem is not None else "",
                        "description": des_elem.text if des_elem is not None else "",
                        "file_size": totallen_elem.text if totallen_elem is not None else "",
                        "attach_id": attachid_elem.text if attachid_elem is not None else "",
                        "file_ext": fileext_elem.text if fileext_elem is not None else "",
                        "type": "app_file"
                    }

                return {
                    "title": title_elem.text if title_elem is not None else "",
                    "description": des_elem.text if des_elem is not None else "",
                    "type": "app_file"
                }

            return {"title": "文件", "content": content}

        except Exception as e:
            logger.error(f"Error parsing app file message: {e}")
            return {"title": "文件", "content": content}

    def _parse_transfer_message(self, content: str) -> Optional[Dict[str, Any]]:
        """解析转账XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return {"title": "转账", "content": content}

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                title_elem = appmsg.find("title")
                des_elem = appmsg.find("des")

                # 查找wcpayinfo节点（转账特有）
                wcpayinfo = appmsg.find("wcpayinfo")
                if wcpayinfo is not None:
                    paysubtype_elem = wcpayinfo.find("paysubtype")
                    feedesc_elem = wcpayinfo.find("feedesc")

                    return {
                        "title": title_elem.text if title_elem is not None else "",
                        "description": des_elem.text if des_elem is not None else "",
                        "pay_subtype": paysubtype_elem.text if paysubtype_elem is not None else "",
                        "fee_desc": feedesc_elem.text if feedesc_elem is not None else "",
                        "type": "transfer"
                    }

                return {
                    "title": title_elem.text if title_elem is not None else "",
                    "description": des_elem.text if des_elem is not None else "",
                    "type": "transfer"
                }

            return {"title": "转账", "content": content}

        except Exception as e:
            logger.error(f"Error parsing transfer message: {e}")
            return {"title": "转账", "content": content}

    def _parse_red_packet_message(self, content: str) -> Optional[Dict[str, Any]]:
        """解析红包XML内容"""
        try:
            if not content or not content.strip().startswith('<'):
                return {"title": "红包", "content": content}

            root = ET.fromstring(content)

            # 查找appmsg节点
            appmsg = root.find("appmsg")
            if appmsg is not None:
                title_elem = appmsg.find("title")
                des_elem = appmsg.find("des")

                # 查找wcpayinfo节点（红包也使用这个节点）
                wcpayinfo = appmsg.find("wcpayinfo")
                if wcpayinfo is not None:
                    paysubtype_elem = wcpayinfo.find("paysubtype")
                    feedesc_elem = wcpayinfo.find("feedesc")

                    return {
                        "title": title_elem.text if title_elem is not None else "",
                        "description": des_elem.text if des_elem is not None else "",
                        "pay_subtype": paysubtype_elem.text if paysubtype_elem is not None else "",
                        "fee_desc": feedesc_elem.text if feedesc_elem is not None else "",
                        "type": "red_packet"
                    }

                return {
                    "title": title_elem.text if title_elem is not None else "",
                    "description": des_elem.text if des_elem is not None else "",
                    "type": "red_packet"
                }

            return {"title": "红包", "content": content}

        except Exception as e:
            logger.error(f"Error parsing red packet message: {e}")
            return {"title": "红包", "content": content}

    async def _process_and_forward_text_message(self, message: StandardMessage) -> bool:
        """
        处理文本消息并转发到指定接口

        Args:
            message: 标准化消息对象

        Returns:
            bool: 是否成功处理和转发
        """
        try:
            # 处理消息内容
            processed_content = self._process_text_content(message.content)

            # 构建转发消息格式
            forward_data = self._build_forward_message(message, processed_content)

            # 转发到HTTP接口
            success = await self._forward_to_http_api(forward_data)

            if success:
                logger.info(f"Successfully forwarded message {message.msg_id} to HTTP API")
                return True
            else:
                logger.error(f"Failed to forward message {message.msg_id} to HTTP API")
                return False

        except Exception as e:
            logger.error(f"Error processing and forwarding text message {message.msg_id}: {e}")
            return False

    def _process_text_content(self, content: str) -> str:
        """
        处理文本内容

        Args:
            content: 原始文本内容

        Returns:
            str: 处理后的文本内容
        """
        # TODO: 在这里添加具体的文本处理逻辑
        # 例如：过滤敏感词、格式化、提取关键信息等

        # 临时处理：简单清理和格式化
        processed = content.strip()

        # 移除多余的空白字符
        processed = ' '.join(processed.split())

        # 这里可以添加更多处理逻辑
        # 例如：
        # - 敏感词过滤
        # - 表情符号处理
        # - 链接提取
        # - 关键词标记

        logger.debug(f"Processed text content: {processed[:100]}...")
        return processed

    def _build_forward_message(self, message: StandardMessage, processed_content: str) -> Dict[str, Any]:
        """
        构建转发消息的数据格式

        Args:
            message: 原始消息对象
            processed_content: 处理后的内容

        Returns:
            Dict: 转发消息的数据结构
        """
        # 生成随机消息ID
        random_msg_id = random.randint(100000, 999999)

        # 获取当前时间戳
        create_time = int(time.time())

        # 构建消息内容，包含发送者信息
        content_with_sender = f"{message.from_user_name}:\n{processed_content}"

        # 构建转发数据结构
        forward_data = {
            "TypeName": "AddMsg",
            "Appid": "wx_K0G1TkCSGAjEd1nLdPr4n",
            "Wxid": "YBA-19990312",
            "Data": {
                "MsgId": random_msg_id,
                "FromUserName": {
                    "string": message.group_id if hasattr(message, 'group_id') and message.group_id else message.from_user_name
                },
                "ToUserName": {
                    "string": "YBA-19990312"
                },
                "MsgType": 1,  # 文本消息
                "Content": {
                    "string": content_with_sender
                },
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {
                    "iLen": 0
                },
                "CreateTime": create_time
            }
        }

        logger.debug(f"Built forward message data for msg_id: {message.msg_id}")
        return forward_data

    async def _forward_to_http_api(self, data: Dict[str, Any]) -> bool:
        """
        转发数据到HTTP API

        Args:
            data: 要转发的数据

        Returns:
            bool: 是否成功转发
        """
        url = "http://cloud.yaoboan.com:2533/api/receive_message"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url,
                    json=data,
                    headers={
                        'Content-Type': 'application/json',
                        'User-Agent': 'WeChatBot/1.0'
                    },
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        response_text = await response.text()
                        logger.debug(f"HTTP API response: {response_text}")
                        return True
                    else:
                        logger.error(f"HTTP API returned status {response.status}: {await response.text()}")
                        return False

        except aiohttp.ClientError as e:
            logger.error(f"HTTP client error when forwarding message: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error when forwarding message: {e}")
            return False

    async def _process_and_forward_announcement(self, message: StandardMessage, announcement_content: str) -> bool:
        """
        处理群公告消息并转发到指定接口

        Args:
            message: 标准化消息对象
            announcement_content: 解析后的群公告内容

        Returns:
            bool: 是否成功处理和转发
        """
        try:
            # 处理群公告内容
            processed_content = self._process_announcement_content(announcement_content)

            # 构建转发消息格式
            forward_data = self._build_forward_announcement(message, processed_content)

            # 转发到HTTP接口
            success = await self._forward_to_http_api(forward_data)

            if success:
                logger.info(f"Successfully forwarded announcement {message.msg_id} to HTTP API")
                return True
            else:
                logger.error(f"Failed to forward announcement {message.msg_id} to HTTP API")
                return False

        except Exception as e:
            logger.error(f"Error processing and forwarding announcement {message.msg_id}: {e}")
            return False

    def _process_announcement_content(self, announcement_content: str) -> str:
        """
        处理群公告内容

        Args:
            announcement_content: 群公告内容字符串

        Returns:
            str: 处理后的群公告内容
        """
        if not announcement_content:
            return "📢 群公告"

        # 构建格式化的群公告内容
        processed = f"📢 群公告\n\n{announcement_content}"

        # 清理和格式化内容
        processed = processed.strip()
        # 保留换行符，只合并多余的空格
        lines = processed.split('\n')
        processed_lines = [' '.join(line.split()) for line in lines]
        processed = '\n'.join(processed_lines)

        logger.debug(f"Processed announcement content: {processed[:100]}...")
        return processed

    def _build_forward_announcement(self, message: StandardMessage, processed_content: str) -> Dict[str, Any]:
        """
        构建转发群公告的数据格式

        Args:
            message: 原始消息对象
            processed_content: 处理后的群公告内容

        Returns:
            Dict: 转发消息的数据结构
        """
        # 生成随机消息ID
        random_msg_id = random.randint(100000, 999999)

        # 获取当前时间戳
        create_time = int(time.time())

        # 构建消息内容，包含发送者信息和群公告标识
        content_with_sender = f"yba@openim:\n{processed_content}"

        # 构建转发数据结构
        forward_data = {
            "TypeName": "AddMsg",
            "Appid": "wx_K0G1TkCSGAjEd1nLdPr4n",
            "Wxid": "YBA-19990312",
            "Data": {
                "MsgId": random_msg_id,
                "FromUserName": {
                    "string": message.group_id if hasattr(message, 'group_id') and message.group_id else message.from_user_name
                },
                "ToUserName": {
                    "string": "YBA-19990312"
                },
                "MsgType": 1,  # 文本消息（群公告也作为文本消息转发）
                "Content": {
                    "string": content_with_sender
                },
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {
                    "iLen": 0
                },
                "CreateTime": create_time
            }
        }

        logger.debug(f"Built forward announcement data for msg_id: {message.msg_id}")
        return forward_data
